@php
$configData = Helper::appClasses();
@endphp
@extends('layouts/layoutMaster')

@section('title', 'Quản lý template')

<!-- Vendor Style -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss'
])
@endsection

<!-- Page Style -->
@section('page-style')
@vite(['resources/css/asset-templates.css'])
@endsection

<!-- Vendor Script -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'
])
@endsection

<!-- Page Script -->
@section('page-script')
@vite([
  'resources/js/common-helpers.js',
  'resources/js/asset-templates.js'
])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header border-bottom">
        <h5 class="card-title mb-0">
          <i class="ri-file-copy-line me-2"></i>
          Quản lý template
        </h5>
        <div class="d-flex justify-content-between align-items-center row pt-4 gap-4 gap-md-0">
          <div class="col-md-4 col-12">
            @can('asset-templates.create')
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#assetTemplateModal">
              <i class="ri-add-line me-1"></i>
              Thêm template mới
            </button>
            @endcan
          </div>
        </div>
      </div>

      <!-- DataTable -->
      <div class="card-datatable table-responsive">
        <table id="assetTemplatesTable" class="datatables-asset-templates table">
          <thead>
            <tr>
              <th>Tên template</th>
              <th>Loại hợp đồng</th>
              <th>Số fields</th>
              <th>Số documents</th>
              <th>Mặc định</th>
              <th>Trạng thái</th>
              <th>Thứ tự</th>
              <th>Thao tác</th>
            </tr>
          </thead>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Asset Template Modal -->
<div class="modal fade" id="assetTemplateModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-fullscreen">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="assetTemplateModalTitle">Thêm template mới</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="assetTemplateForm">
        <div class="modal-body">
          <input type="hidden" id="assetTemplateId" name="id">

          <!-- Basic Information -->
          <div class="row g-3 mb-4">
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <select id="assetTemplateContractType" name="contract_type_id" class="form-select" required>
                  <option value="">Chọn loại hợp đồng</option>
                  @foreach($contractTypes as $contractType)
                    <option value="{{ $contractType->id }}">{{ $contractType->name }}</option>
                  @endforeach
                </select>
                <label for="assetTemplateContractType">Loại hợp đồng *</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="assetTemplateName" name="name" class="form-control" required>
                <label for="assetTemplateName">Tên template *</label>
              </div>
            </div>
            <div class="col-12">
              <div class="form-floating form-floating-outline">
                <textarea id="assetTemplateDescription" name="description" class="form-control" rows="3"></textarea>
                <label for="assetTemplateDescription">Mô tả</label>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-floating form-floating-outline">
                <input type="number" id="assetTemplateSortOrder" name="sort_order" class="form-control" min="0" value="0">
                <label for="assetTemplateSortOrder">Thứ tự sắp xếp</label>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-check form-switch mt-3">
                <input type="checkbox" class="form-check-input" id="assetTemplateIsDefault" name="is_default">
                <label class="form-check-label" for="assetTemplateIsDefault">Template mặc định</label>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-check form-switch mt-3">
                <input type="checkbox" class="form-check-input" id="assetTemplateIsActive" name="is_active" checked>
                <label class="form-check-label" for="assetTemplateIsActive">Hoạt động</label>
              </div>
            </div>
          </div>

          <!-- Fields Management -->
          <div class="fields-container">
            <div class="available-fields">
              <h6>
                <i class="ri-list-check me-2"></i>
                Fields có sẵn
              </h6>
              <div id="availableFieldsList" class="fields-list">
                <!-- Available fields will be loaded here -->
              </div>
            </div>

            <div class="template-fields">
              <h6>
                <i class="ri-drag-drop-line me-2"></i>
                Fields trong template
              </h6>
              <div id="templateFieldsList" class="fields-list">
                <div class="drop-zone" id="templateDropZone">
                  <div class="drop-zone-text">
                    <i class="ri-drag-drop-line mb-2" style="font-size: 2rem; opacity: 0.5;"></i>
                    <p class="mb-0">Kéo thả fields từ bên trái vào đây</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Template Preview -->
          <div class="template-preview" id="templatePreview" style="display: none;">
            <div class="template-preview-title">
              <i class="ri-eye-line me-2"></i>
              Preview Template
            </div>
            <div id="templatePreviewContent">
              <!-- Preview content will be generated here -->
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="button" class="btn btn-outline-info" id="previewTemplateBtn">
            <i class="ri-eye-line me-1"></i>
            Preview
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="ri-save-line me-1"></i>
            Lưu
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Xác nhận xóa</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Bạn có chắc chắn muốn xóa template này không?</p>
        <p class="text-danger"><small>Hành động này không thể hoàn tác.</small></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Xóa</button>
      </div>
    </div>
  </div>
</div>

@push('scripts')
<script>
// Pass Laravel routes to JavaScript
window.assetTemplatesRoutes = {
  index: '{{ route("asset-templates.index") }}',
  store: '{{ route("asset-templates.store") }}',
  show: function(id) { return `/asset-templates/${id}`; },
  update: function(id) { return `/asset-templates/${id}`; },
  destroy: function(id) { return `/asset-templates/${id}`; }
};

// Pass asset fields route
window.assetFieldsRoute = '{{ route("asset-fields.index") }}';

// Pass contract types to JavaScript
window.contractTypes = @json($contractTypes);
</script>
@endpush
@endsection
