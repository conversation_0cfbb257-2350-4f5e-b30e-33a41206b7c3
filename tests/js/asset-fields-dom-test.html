<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Fields DOM Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .test-result {
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 0.375rem;
        }
        .test-pass {
            background-color: #d1e7dd;
            border: 1px solid #badbcc;
            color: #0f5132;
        }
        .test-fail {
            background-color: #f8d7da;
            border: 1px solid #f5c2c7;
            color: #842029;
        }
        .console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 1rem;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Asset Fields JavaScript DOM Test</h1>
        <p class="text-muted">Test để kiểm tra DOM manipulation functions hoạt động đúng cách với null checking</p>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test Cases</h3>
                <button class="btn btn-primary mb-2" onclick="runAllTests()">Chạy tất cả tests</button>
                <button class="btn btn-secondary mb-2" onclick="clearResults()">Xóa kết quả</button>
                
                <div class="list-group">
                    <button class="list-group-item list-group-item-action" onclick="testResetFormWithoutModal()">
                        Test 1: resetForm() không có modal
                    </button>
                    <button class="list-group-item list-group-item-action" onclick="testResetFormWithModal()">
                        Test 2: resetForm() có modal
                    </button>
                    <button class="list-group-item list-group-item-action" onclick="testGetOptionsFromFormEmpty()">
                        Test 3: getOptionsFromForm() không có options
                    </button>
                    <button class="list-group-item list-group-item-action" onclick="testGetOptionsFromFormWithData()">
                        Test 4: getOptionsFromForm() có data
                    </button>
                    <button class="list-group-item list-group-item-action" onclick="testHandleFieldTypeChange()">
                        Test 5: handleFieldTypeChange() không có elements
                    </button>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>Kết quả Test</h3>
                <div id="testResults"></div>
                
                <h4 class="mt-4">Console Output</h4>
                <div id="consoleOutput" class="console-output"></div>
            </div>
        </div>
        
        <!-- Mock Modal Structure -->
        <div id="mockModal" style="display: none;">
            <div class="field-options-section">
                <div class="options-container">
                    <div class="option-item">
                        <input name="options[0][key]" value="key1">
                        <input name="options[0][value]" value="value1">
                    </div>
                    <div class="option-item">
                        <input name="options[1][key]" value="key2">
                        <input name="options[1][value]" value="value2">
                    </div>
                </div>
            </div>
            <div class="field-preview">Preview content</div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Mock global variables
        let isEditMode = false;
        let optionIndex = 0;
        
        // Console capture
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error
        };
        
        let consoleOutput = [];
        
        function captureConsole() {
            console.log = (...args) => {
                consoleOutput.push('LOG: ' + args.join(' '));
                originalConsole.log(...args);
            };
            console.warn = (...args) => {
                consoleOutput.push('WARN: ' + args.join(' '));
                originalConsole.warn(...args);
            };
            console.error = (...args) => {
                consoleOutput.push('ERROR: ' + args.join(' '));
                originalConsole.error(...args);
            };
        }
        
        function restoreConsole() {
            console.log = originalConsole.log;
            console.warn = originalConsole.warn;
            console.error = originalConsole.error;
        }
        
        // Asset Fields Functions (simplified versions for testing)
        function resetForm() {
            isEditMode = false;
            optionIndex = 0;
            
            // Clear options and hide options section - with null checking
            const optionsContainer = document.querySelector('.options-container');
            const optionsSection = document.querySelector('.field-options-section');
            
            if (optionsContainer) {
                optionsContainer.innerHTML = '';
            }
            
            if (optionsSection) {
                optionsSection.style.display = 'none';
            }

            // Remove preview - with null checking
            const preview = document.querySelector('.field-preview');
            if (preview) {
                preview.remove();
            }
        }
        
        function getOptionsFromForm() {
            const options = [];
            document.querySelectorAll('.option-item').forEach(item => {
                const keyInput = item.querySelector('[name*="[key]"]');
                const valueInput = item.querySelector('[name*="[value]"]');
                
                if (keyInput && valueInput) {
                    const key = keyInput.value;
                    const value = valueInput.value;
                    if (key && value) {
                        options.push({ key, value });
                    }
                }
            });
            return options;
        }
        
        function handleFieldTypeChange(type) {
            const optionsSection = document.querySelector('.field-options-section');
            const optionsContainer = document.querySelector('.options-container');

            // Check if elements exist - don't warn if modal is not open
            if (!optionsSection || !optionsContainer) {
                return;
            }

            const needsOptions = ['select', 'radio', 'checkbox'].includes(type);

            if (needsOptions) {
                optionsSection.style.display = 'block';
            } else {
                optionsSection.style.display = 'none';
                optionsContainer.innerHTML = '';
            }
        }
        
        // Test Functions
        function addTestResult(testName, passed, message) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'test-pass' : 'test-fail'}`;
            resultDiv.innerHTML = `
                <strong>${testName}</strong><br>
                ${passed ? '✅ PASS' : '❌ FAIL'}: ${message}
            `;
            resultsDiv.appendChild(resultDiv);
        }
        
        function updateConsoleOutput() {
            const consoleDiv = document.getElementById('consoleOutput');
            consoleDiv.textContent = consoleOutput.join('\n');
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        function testResetFormWithoutModal() {
            captureConsole();
            consoleOutput = [];
            
            try {
                resetForm();
                addTestResult('Test 1', true, 'resetForm() hoạt động không lỗi khi không có modal elements');
            } catch (error) {
                addTestResult('Test 1', false, `resetForm() gây lỗi: ${error.message}`);
            }
            
            updateConsoleOutput();
            restoreConsole();
        }
        
        function testResetFormWithModal() {
            captureConsole();
            consoleOutput = [];
            
            // Add mock modal to DOM
            document.body.appendChild(document.getElementById('mockModal').cloneNode(true));
            
            try {
                resetForm();
                
                // Check if elements were properly handled
                const container = document.querySelector('.options-container');
                const section = document.querySelector('.field-options-section');
                const preview = document.querySelector('.field-preview');
                
                const containerCleared = !container || container.innerHTML === '';
                const sectionHidden = !section || section.style.display === 'none';
                const previewRemoved = !preview;
                
                if (containerCleared && sectionHidden && previewRemoved) {
                    addTestResult('Test 2', true, 'resetForm() xử lý đúng các modal elements');
                } else {
                    addTestResult('Test 2', false, 'resetForm() không xử lý đúng modal elements');
                }
            } catch (error) {
                addTestResult('Test 2', false, `resetForm() gây lỗi với modal: ${error.message}`);
            }
            
            // Cleanup
            const addedModal = document.body.querySelector('#mockModal');
            if (addedModal) {
                addedModal.remove();
            }
            
            updateConsoleOutput();
            restoreConsole();
        }
        
        function testGetOptionsFromFormEmpty() {
            captureConsole();
            consoleOutput = [];
            
            try {
                const options = getOptionsFromForm();
                if (Array.isArray(options) && options.length === 0) {
                    addTestResult('Test 3', true, 'getOptionsFromForm() trả về array rỗng khi không có options');
                } else {
                    addTestResult('Test 3', false, `getOptionsFromForm() trả về: ${JSON.stringify(options)}`);
                }
            } catch (error) {
                addTestResult('Test 3', false, `getOptionsFromForm() gây lỗi: ${error.message}`);
            }
            
            updateConsoleOutput();
            restoreConsole();
        }
        
        function testGetOptionsFromFormWithData() {
            captureConsole();
            consoleOutput = [];
            
            // Add mock modal with data to DOM
            document.body.appendChild(document.getElementById('mockModal').cloneNode(true));
            
            try {
                const options = getOptionsFromForm();
                if (Array.isArray(options) && options.length === 2) {
                    addTestResult('Test 4', true, `getOptionsFromForm() trả về đúng data: ${JSON.stringify(options)}`);
                } else {
                    addTestResult('Test 4', false, `getOptionsFromForm() trả về sai: ${JSON.stringify(options)}`);
                }
            } catch (error) {
                addTestResult('Test 4', false, `getOptionsFromForm() gây lỗi với data: ${error.message}`);
            }
            
            // Cleanup
            const addedModal = document.body.querySelector('#mockModal');
            if (addedModal) {
                addedModal.remove();
            }
            
            updateConsoleOutput();
            restoreConsole();
        }
        
        function testHandleFieldTypeChange() {
            captureConsole();
            consoleOutput = [];
            
            try {
                handleFieldTypeChange('select');
                addTestResult('Test 5', true, 'handleFieldTypeChange() hoạt động không lỗi khi không có elements');
            } catch (error) {
                addTestResult('Test 5', false, `handleFieldTypeChange() gây lỗi: ${error.message}`);
            }
            
            updateConsoleOutput();
            restoreConsole();
        }
        
        function runAllTests() {
            clearResults();
            testResetFormWithoutModal();
            setTimeout(() => testResetFormWithModal(), 100);
            setTimeout(() => testGetOptionsFromFormEmpty(), 200);
            setTimeout(() => testGetOptionsFromFormWithData(), 300);
            setTimeout(() => testHandleFieldTypeChange(), 400);
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('consoleOutput').textContent = '';
            consoleOutput = [];
        }
        
        // Initialize
        captureConsole();
    </script>
</body>
</html>
