# JavaScript DOM Manipulation Best Practices - Laravel Project

## Tổng quan

Tài liệu này cung cấp hướng dẫn chi tiết về các tiêu chuẩn phát triển JavaScript cho dự án <PERSON>, đặc biệt tập trung vào DOM manipulation và error handling.

## 1. <PERSON><PERSON>u trúc thư mục và tổ chức tài nguyên

### 1.1 Tổ chức JavaScript
```
resources/
├── js/
│   ├── asset-fields.js      # Quản lý dynamic fields
│   ├── asset-templates.js   # Quản lý templates
│   ├── qr-scanner.js       # QR code scanning
│   └── [module-name].js    # Các modules khác
├── css/
│   ├── asset-fields.css    # Styles cho asset fields
│   ├── asset-templates.css # Styles cho templates
│   └── [module-name].css   # Styles cho modules khác
└── views/
    └── template/           # HTML templates tham chiếu
```

### 1.2 Quy tắc đặt tên file
- Sử dụng kebab-case cho tên file: `asset-fields.js`, `user-management.js`
- Tên file phải phản ánh chức năng chính của module
- Mỗi page/feature có một file JavaScript riêng biệt

## 2. DOM Manipulation Best Practices

### 2.1 Null Checking - Bắt buộc cho tất cả DOM operations

**❌ Sai - Không kiểm tra null:**
```javascript
function resetForm() {
  // Có thể gây lỗi "Cannot set properties of null"
  document.querySelector('.options-container').innerHTML = '';
  document.querySelector('.field-options-section').style.display = 'none';
}
```

**✅ Đúng - Có null checking:**
```javascript
function resetForm() {
  // Kiểm tra null trước khi thao tác
  const optionsContainer = document.querySelector('.options-container');
  const optionsSection = document.querySelector('.field-options-section');
  
  if (optionsContainer) {
    optionsContainer.innerHTML = '';
  }
  
  if (optionsSection) {
    optionsSection.style.display = 'none';
  }
}
```

### 2.2 Pattern chuẩn cho DOM element checking

```javascript
// Pattern 1: Single element check
const element = document.querySelector('.selector');
if (element) {
  // Thực hiện thao tác
  element.innerHTML = 'content';
}

// Pattern 2: Multiple elements check
const container = document.querySelector('.container');
const button = document.querySelector('.button');

if (!container || !button) {
  return; // Exit gracefully nếu thiếu elements
}

// Tiếp tục xử lý khi tất cả elements đều tồn tại
```

### 2.3 Xử lý NodeList và querySelector

**✅ An toàn với querySelectorAll:**
```javascript
function getOptionsFromForm() {
  const options = [];
  document.querySelectorAll('.option-item').forEach(item => {
    const keyInput = item.querySelector('[name*="[key]"]');
    const valueInput = item.querySelector('[name*="[value]"]');
    
    // Kiểm tra null cho từng input
    if (keyInput && valueInput) {
      const key = keyInput.value;
      const value = valueInput.value;
      if (key && value) {
        options.push({ key, value });
      }
    }
  });
  return options;
}
```

## 3. Event Handling Standards

### 3.1 Modal Event Handlers
```javascript
function initializeEventHandlers() {
  // Modal reset khi đóng
  $('#assetFieldModal').on('hidden.bs.modal', function() {
    resetForm(); // Function này phải có null checking
  });
  
  // Modal show event để khởi tạo elements
  $('#assetFieldModal').on('shown.bs.modal', function() {
    initializeModalElements();
  });
}
```

### 3.2 Dynamic Element Events
```javascript
// Sử dụng event delegation cho dynamic elements
$(document).on('click', '.add-option-btn', function() {
  addFieldOption();
});

$(document).on('click', '.remove-option-btn', function() {
  $(this).closest('.option-item').remove();
  updateFieldPreview();
});
```

## 4. Error Handling và Debugging

### 4.1 Graceful Error Handling
```javascript
function handleFieldTypeChange(type) {
  const optionsSection = document.querySelector('.field-options-section');
  const optionsContainer = document.querySelector('.options-container');

  // Graceful exit nếu elements không tồn tại
  if (!optionsSection || !optionsContainer) {
    return; // Không log warning - modal có thể chưa mở
  }

  // Tiếp tục xử lý logic
  const needsOptions = ['select', 'radio', 'checkbox'].includes(type);
  // ...
}
```

### 4.2 Console Logging Guidelines
```javascript
// ❌ Tránh log warnings cho missing elements khi modal chưa mở
if (!element) {
  console.warn('Element not found'); // Không nên làm
  return;
}

// ✅ Silent return cho missing elements
if (!element) {
  return; // Graceful exit
}

// ✅ Log errors cho unexpected conditions
if (data.length === 0) {
  console.error('No data received from server');
  showToast('error', 'Không có dữ liệu');
}
```

## 5. DataTables Implementation

### 5.1 Client-side DataTables (Preferred)
```javascript
function initializeDataTable() {
  assetFieldsTable = $('#assetFieldsTable').DataTable({
    ajax: {
      url: window.assetFieldsRoutes.index,
      type: 'GET'
    },
    columns: [
      { data: 'name' },
      { data: 'label' },
      { data: 'type' },
      // ...
    ],
    dom: 'Bfrtip',
    buttons: ['copy', 'csv', 'excel', 'pdf', 'print'],
    language: {
      url: '/assets/vendor/libs/datatables-bs5/i18n/vi.json'
    }
  });
}
```

### 5.2 AJAX Data Loading
```javascript
// Load data via AJAX thay vì server-side processing
ajax: {
  url: '/api/asset-fields',
  type: 'GET',
  dataSrc: 'data' // Specify data source property
}
```

## 6. Form Components Standards

### 6.1 Select2 Implementation
```javascript
// Khởi tạo Select2 cho dropdowns
$('.select2').select2({
  theme: 'bootstrap-5',
  placeholder: 'Chọn...',
  allowClear: true
});

// Select2 với AJAX data loading
$('.select2-ajax').select2({
  ajax: {
    url: '/api/search',
    dataType: 'json',
    delay: 250,
    processResults: function (data) {
      return {
        results: data.items
      };
    }
  }
});
```

### 6.2 Form Validation
```javascript
function validateField(field) {
  const $field = $(field);
  const value = $field.val().trim();

  $field.removeClass('is-invalid is-valid');

  if (field.required && !value) {
    $field.addClass('is-invalid');
    return false;
  }

  if (value) {
    $field.addClass('is-valid');
  }

  return true;
}
```

## 7. Vite Integration

### 7.1 Asset Registration trong Blade
```php
@section('page-script')
@vite(['resources/js/asset-fields.js'])
@endsection

@section('page-style')
@vite(['resources/css/asset-fields.css'])
@endsection
```

### 7.2 Vite Configuration
```javascript
// vite.config.js
export default defineConfig({
  plugins: [
    laravel({
      input: [
        'resources/css/app.css',
        'resources/js/app.js',
        'resources/js/asset-fields.js',
        'resources/css/asset-fields.css'
      ],
      refresh: true,
    }),
  ],
});
```

## 8. Testing Guidelines

### 8.1 Manual Testing Checklist
- [ ] Mở trang không có console errors
- [ ] Mở modal không có JavaScript errors  
- [ ] Thay đổi field type hoạt động smooth
- [ ] Thêm/xóa options không gây lỗi
- [ ] Submit form thành công
- [ ] Đóng modal reset form đúng cách

### 8.2 Browser Compatibility
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 9. Performance Best Practices

### 9.1 DOM Query Optimization
```javascript
// ❌ Tránh query DOM nhiều lần
function updateMultipleElements() {
  document.querySelector('.element1').style.display = 'block';
  document.querySelector('.element1').innerHTML = 'content';
  document.querySelector('.element1').classList.add('active');
}

// ✅ Cache DOM reference
function updateMultipleElements() {
  const element = document.querySelector('.element1');
  if (element) {
    element.style.display = 'block';
    element.innerHTML = 'content';
    element.classList.add('active');
  }
}
```

### 9.2 Event Delegation
```javascript
// ✅ Sử dụng event delegation cho dynamic content
$(document).on('click', '.dynamic-button', handleClick);

// Thay vì bind events cho từng element
$('.dynamic-button').on('click', handleClick); // Không hoạt động với dynamic content
```

## 10. Code Review Checklist

- [ ] Tất cả DOM selectors có null checking
- [ ] Không có inline JavaScript/CSS trong Blade files
- [ ] Event handlers được setup đúng cách
- [ ] Error handling graceful và user-friendly
- [ ] Code tuân theo naming conventions
- [ ] Functions có JSDoc comments
- [ ] No console.log statements trong production code
- [ ] DataTables sử dụng client-side processing
- [ ] Select2 được implement đúng cách
- [ ] Vite assets được register đúng
